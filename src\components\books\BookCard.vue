<template>
  <article
    class="book"
    :class="{ 'loading-wave': book.isLoading }"
    @click="book.isLoading ? null : handleClick"
    :aria-busy="book.isLoading"
  >
    <div class="book-container">
      <!-- Loading State with Skeleton Placeholders -->
      <template v-if="book.isLoading">
        <div class="book-cover-placeholder"></div>
        <div class="placeholder-text-lg placeholder-block"></div>
        <div class="placeholder-text-md placeholder-block"></div>
        <div class="placeholder-divider-line"></div>
        <div class="placeholder-text-md placeholder-block" style="width: 40%; margin-bottom: 8px;"></div>
        <div class="placeholder-note-area"></div>
        <div class="placeholder-rating-section">
          <div class="placeholder-text-sm placeholder-block"></div>
          <div class="placeholder-text-sm placeholder-block"></div>
        </div>
      </template>

      <!-- Loaded State with Content -->
      <template v-else>
        <div class="book-cover animate-reveal" :style="{ animationDelay: '0s' }">
          <!-- Blurred background for better visual when cover doesn't fill container -->
          <div v-if="hasValidCover" class="blurred-background" :style="blurredBackgroundStyle"></div>

          <!-- Main book cover image on top -->
          <div v-if="hasValidCover" class="main-cover" :style="coverImageStyle"></div>

          <div v-if="!hasValidCover" class="default-cover">
            <svg width="32" height="40" viewBox="0 0 32 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="32" height="40" rx="2" fill="var(--color-border-primary)"/>
              <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="var(--color-text-muted)"/>
            </svg>
          </div>
        </div>

        <div class="book-title-author-block animate-reveal" :style="{ animationDelay: '0.05s' }">
          <h2 class="book-title">{{ book.title }}</h2>
          <p class="author">{{ book.author || 'Unknown Author' }}</p>
        </div>

        <div class="divider-block animate-reveal" :style="{ animationDelay: '0.08s' }">
          <hr class="divider" />
        </div>

        <div class="recent-note-heading-block animate-reveal" :style="{ animationDelay: '0.1s' }">
          <h3 class="recent-note">Recent Note</h3>
        </div>

        <div class="note-container-block animate-reveal" :style="{ animationDelay: '0.12s' }">
          <div class="note-container" v-if="book.recentNote">
            <p class="note-title">{{ book.recentNote.title }}</p>
            <img
              src="/icons/recent-icon.svg"
              alt="Open recent note"
              class="recent-note-icon"
              @click.stop="openRecentNote"
              @keydown="event => handleIconKeyDown(event, openRecentNote)"
              title="Open this note"
              role="button"
              tabindex="0"
            />
          </div>
          <div class="note-container" v-else>
            <p class="note-title">No notes yet</p>
          </div>
        </div>

        <div class="rating-section-block animate-reveal" :style="{ animationDelay: '0.15s' }">
          <div class="rating-section">
            <div class="rating-container">
              <p class="rating-text">Your rating: {{ book.rating || 0 }}/5</p>
              <img
                src="/icons/star-icon.svg"
                alt="Rating icon"
                class="icon"
              />
            </div>
            <div class="interaction-container">
              <img
                src="/icons/notes-icon.svg"
                alt="Notes icon"
                class="icon"
              />
              <span class="interaction-count" title="Number of notes">{{ book.notesCount || 0 }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </article>
</template>

<script lang="ts">
import { defineComponent, PropType, computed } from 'vue'
import type { BookWithNoteCount } from '../../types/electron-api'

export default defineComponent({
  name: 'BookCard',
  props: {
    book: {
      type: Object as PropType<BookWithNoteCount>,
      required: true
    }  },
  emits: ['delete', 'click', 'openNote'],
  setup(props, { emit }) {
    const hasValidCover = computed(() => {
      return !!(props.book.cover_media_url || props.book.cover_url)
    })

    const coverImageStyle = computed(() => {
      // Priority: cover_media_url > cover_url
      if (props.book.cover_media_url) {
        // Use stored cover image from media_files table
        return {
          backgroundImage: `url(${props.book.cover_media_url})`,
          backgroundSize: 'auto 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }
      }

      if (props.book.cover_url) {
        // Fallback to online cover URL
        return {
          backgroundImage: `url(${props.book.cover_url})`,
          backgroundSize: 'auto 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }
      }      return {}
    })
    
    const blurredBackgroundStyle = computed(() => {
      if (!hasValidCover.value) return {}

      const imageUrl = props.book.cover_media_url || props.book.cover_url
      if (!imageUrl) return {}
      
      return {
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    })

    const handleDelete = () => {
      emit('delete', props.book)
    }
    
    const handleClick = () => {
      // Don't emit click for books that are loading or have temporary IDs
      if (props.book.isLoading || (props.book.id && props.book.id > 1000000000000)) {
        console.log('Book is loading or has temporary ID, click ignored');
        return;
      }      emit('click', props.book)
    }
    const openRecentNote = (event: MouseEvent) => {
      // Only proceed if book has a recent note
      if (!props.book.recentNote) return;

      // Prevent the click event from bubbling up to the book card
      event.stopPropagation();

      // Emit a specific event for opening a note directly
      emit('openNote', props.book.recentNote);
    }

    const handleIconKeyDown = (event: KeyboardEvent, action: (event: Event) => void) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        action(event);
      }
    }

    return {
      hasValidCover,
      coverImageStyle,
      blurredBackgroundStyle,
      handleDelete,
      handleClick,
      openRecentNote,
      handleIconKeyDown
    }
  }
})
</script>

<style scoped>
.book {
  width: 100%;
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 700;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.book:hover:not(.loading-wave) {
  transform: translateY(-2px);
}

.book:focus-visible:not(.loading-wave) {
  outline: 2px solid var(--color-primary);
  outline-offset: 3px;
  border-radius: 10px;
  transform: translateY(-2px);
}

.book.loading-wave {
  cursor: not-allowed;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.book-container {
  border-radius: 10px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  display: flex;
  width: 100%;
  padding: 12px;
  flex-direction: column;
  align-items: start;
  box-sizing: border-box;
}

.book-cover {
  border-radius: 5px;
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  width: 100%;
  height: 400px; /* Further increased height for taller book cover */
  max-width: 100%;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.blurred-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(10px) brightness(0.7);
  transform: scale(1.1);
  z-index: 0;
  opacity: 0.8;
}

.main-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: auto 100%;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
  /* Add mask for feathered edge blending */
  mask: radial-gradient(ellipse 85% 90% at center, black 100%, transparent 100%);
  -webkit-mask: radial-gradient(ellipse 85% 90% at center, black 100%, transparent 100%);
}

.book-cover > *:not(.blurred-background) {
  position: relative;
  z-index: 1;
}

.default-cover {
  background-color: var(--color-bg-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.book-title {
  color: var(--color-text-primary);
  font-size: 16px;
  margin-top: 12px;
  margin-bottom: 0;
  font-weight: 600;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.author {
  font-weight: 400;
  margin-top: 3px;
  margin-bottom: 0;
  color: var(--color-text-secondary);
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.divider {
  background-color: var(--color-border-primary);
  width: 100%;
  height: 1px;
  border: none;
  margin: 10px 0;
  flex-shrink: 0;
}

.recent-note {
  font-weight: 600;
  color: var(--color-text-secondary);
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 0 8px 0;
  font-size: 12px;
}

.note-container {
  border-radius: 5px;
  background-color: var(--color-bg-tertiary);
  width: 100%;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  flex-shrink: 0;
  margin-bottom: 15px; /* Add space between note container and ratings */
}

.note-container .note-title {
  color: var(--color-text-primary) !important;
  font-weight: 400;
  width: calc(100% - 20px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
  font-size: 12px;
}

.recent-note-icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.recent-note-icon:hover {
  opacity: 1;
}

.recent-note-icon:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 1px;
  border-radius: 3px;
  opacity: 1;
}

.rating-section {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  font-weight: 400;
  flex-shrink: 0;
  padding-top: 3px;
  font-size: 11px;
}

.rating-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.interaction-container {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.rating-text {
  color: var(--color-text-secondary);
  margin: 0;
}

.icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
  object-position: center;
}

.interaction-count {
  color: var(--color-text-secondary);
}

.book-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.delete-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

/* Placeholder Styles (during loading) */
.book-cover-placeholder {
  width: 100%;
  height: 400px;
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-card-border);
  border-radius: 5px;
  margin-bottom: 12px;
  flex-shrink: 0;
  animation: subtlePulse 2.5s ease-in-out infinite;
}

.placeholder-block {
  background-color: var(--color-bg-tertiary);
  border-radius: 4px;
  min-height: 1em;
  animation: subtlePulse 2.5s ease-in-out infinite;
}

@keyframes subtlePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.placeholder-text-lg {
  height: 20px;
  width: 75%;
  margin-top: 12px;
  margin-bottom: 0px;
}

.placeholder-text-md {
  height: 16px;
  width: 60%;
  margin-top: 3px;
  margin-bottom: 0px;
}

.placeholder-text-sm {
  height: 12px;
  width: 45%;
  margin-top: 3px;
  margin-bottom: 3px;
}

.placeholder-divider-line {
  height: 1px;
  width: 100%;
  margin: 10px 0;
  background-color: var(--color-border-primary);
}

.placeholder-note-area {
  height: 38px;
  width: 100%;
  margin-bottom: 15px;
  border-radius: 5px;
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-primary);
  box-sizing: border-box;
  padding: 6px 10px;
  display: flex;
  align-items: center;
}

.placeholder-rating-section {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding-top: 3px;
}

.placeholder-rating-section .placeholder-text-sm:first-child {
  width: 40%;
  margin: 0;
}

.placeholder-rating-section .placeholder-text-sm:last-child {
  width: 20%;
  margin: 0;
}

/* Subtle shimmer animation for loading books */
.loading-wave::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.03) 20%,
    rgba(255, 255, 255, 0.06) 50%,
    rgba(255, 255, 255, 0.03) 80%,
    transparent 100%
  );
  animation: subtleWave 3.0s ease-in-out infinite;
  z-index: 10;
  border-radius: 10px;
}

/* Dark theme shimmer - uses theme-aware approach */
.theme-dark .loading-wave::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.02) 20%,
    rgba(255, 255, 255, 0.04) 50%,
    rgba(255, 255, 255, 0.02) 80%,
    transparent 100%
  );
}

@keyframes subtleWave {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Subtle Content Reveal Animation */
@keyframes subtleContentReveal {
  0% {
    opacity: 0;
    transform: translateY(3px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

.animate-reveal {
  opacity: 0;
  animation-name: subtleContentReveal;
  animation-duration: 0.3s;
  animation-timing-function: ease-out;
  animation-fill-mode: forwards;
}

/* Ensure proper margins for animated blocks */
.book-title-author-block.animate-reveal {
  margin-top: 0;
}

.divider-block.animate-reveal {
  margin-top: 0;
}

.recent-note-heading-block.animate-reveal {
  margin-top: 0;
}

.note-container-block.animate-reveal {
  margin-top: 0;
}

.rating-section-block.animate-reveal {
  margin-top: 0;
}
</style>
